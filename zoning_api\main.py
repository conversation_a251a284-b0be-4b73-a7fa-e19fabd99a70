import pandas as pd
import numpy as np
import math
import time
import logging
from typing import List, Dict, Tuple, Optional
from fastapi import FastAPI, HTTPException
from fastapi.responses import HTMLResponse
from pydantic import BaseModel, Field
import uvicorn
from sklearn.cluster import KMeans

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# FastAPI app initialization
app = FastAPI(
    title="Zoning API",
    description="API for creating service zones using dynamic clustering",
    version="1.0.0"
)

# Configuration
MAX_CUSTOMERS_PER_ZONE = 11  # Optimal customers per zone

# Pydantic models for API responses
class ZoneCustomer(BaseModel):
    id: int = Field(..., description="Customer ID")
    latitude: float = Field(..., description="Customer latitude")
    longitude: float = Field(..., description="Customer longitude")
    address: str = Field(..., description="Customer address")

class DynamicZone(BaseModel):
    zone_id: int = Field(..., description="Zone identifier")
    customers: List[ZoneCustomer] = Field(..., description="Customers assigned to this zone")
    customer_count: int = Field(..., description="Number of customers in zone")
    center_latitude: float = Field(..., description="Zone center latitude")
    center_longitude: float = Field(..., description="Zone center longitude")

class DynamicZoningResponse(BaseModel):
    total_customers: int = Field(..., description="Total number of customers processed")
    routable_customers: int = Field(..., description="Number of customers with routable coordinates")
    total_zones: int = Field(..., description="Total number of zones created")
    max_customers_per_zone: int = Field(..., description="Maximum customers allowed per zone")
    zones: List[DynamicZone] = Field(..., description="List of all zones with their customers")
    computation_time_ms: float = Field(..., description="Time taken to compute zones in milliseconds")

def load_and_process_customers(filepath: str) -> List[Dict]:
    """Load customer data and create customers list"""
    logger.info(f"Loading data from {filepath}...")
    df = pd.read_csv(filepath)
    logger.info(f"Loaded {len(df)} customer records")

    # Create customers list with correct column names
    customers = [
        {
            "id": idx,
            "address": f"Customer {idx}",
            "coords": [
                float(row['LON']),
                float(row['LAT'])
            ]
        }
        for idx, row in df.iterrows()
    ]
    return customers

def calculate_dynamic_zones(num_customers: int, max_per_zone: int = MAX_CUSTOMERS_PER_ZONE) -> int:
    """Calculate optimal number of zones for dynamic clustering"""
    return math.ceil(num_customers / max_per_zone)

def dynamic_cluster_customers(customers: List[Dict], max_per_zone: int = MAX_CUSTOMERS_PER_ZONE) -> Tuple[List[List[Dict]], np.ndarray, float]:
    """
    Improved clustering that ensures customers are assigned to their closest technician.
    Uses iterative optimization to minimize customer-to-technician distances.
    """
    start_time = time.time()

    if not customers:
        return [], np.array([]), 0.0

    # Calculate number of zones needed
    num_zones = calculate_dynamic_zones(len(customers), max_per_zone)

    # Extract coordinates for clustering
    coords = np.array([customer["coords"] for customer in customers])

    # Step 1: Initial K-means clustering to get starting zone centers
    kmeans = KMeans(n_clusters=num_zones, n_init=10, random_state=42)
    kmeans.fit(coords)
    zone_centers = kmeans.cluster_centers_.copy()

    logger.info(f"Initial K-means clustering completed, now optimizing for closest assignments...")

    # Step 2: Iterative optimization to ensure customers are assigned to closest technician
    max_iterations = 10
    for iteration in range(max_iterations):
        # Create zones based on current centers
        zones = [[] for _ in range(num_zones)]

        # Assign each customer to closest zone center that has capacity
        for customer in customers:
            customer_coord = np.array(customer["coords"])

            # Calculate distances to all zone centers
            zone_distances = []
            for zone_idx, center in enumerate(zone_centers):
                dist = np.sqrt(np.sum((customer_coord - center) ** 2))
                zone_distances.append((dist, zone_idx))

            # Sort by distance (closest first)
            zone_distances.sort(key=lambda x: x[0])

            # Assign to closest zone that has capacity
            assigned = False
            for dist, zone_idx in zone_distances:
                if len(zones[zone_idx]) < max_per_zone:
                    customer_copy = customer.copy()
                    customer_copy["zone_id"] = zone_idx
                    zones[zone_idx].append(customer_copy)
                    assigned = True
                    break

            # If no zone has capacity (shouldn't happen), assign to closest anyway
            if not assigned:
                closest_zone = zone_distances[0][1]
                customer_copy = customer.copy()
                customer_copy["zone_id"] = closest_zone
                zones[closest_zone].append(customer_copy)

        # Step 3: Balance zones by distance
        zones = balance_zones_by_distance(zones, max_per_zone, zone_centers)

        # Step 4: Recalculate zone centers based on new assignments
        new_centers = []
        for zone in zones:
            if zone:  # Non-empty zone
                zone_coords = np.array([c["coords"] for c in zone])
                center = np.mean(zone_coords, axis=0)
                new_centers.append(center)
            else:
                # Keep original center for empty zones
                new_centers.append(zone_centers[len(new_centers)])

        # Check for convergence
        center_movement = np.mean([np.linalg.norm(new - old) for new, old in zip(new_centers, zone_centers)])
        zone_centers = np.array(new_centers)

        if center_movement < 0.001:  # Convergence threshold
            logger.info(f"Converged after {iteration + 1} iterations")
            break

    # Final verification: ensure no zone exceeds max_per_zone
    max_zone_size = max(len(zone) for zone in zones if zone)
    min_zone_size = min(len(zone) for zone in zones if zone)

    logger.info(f"Final zone sizes - Max: {max_zone_size}, Min: {min_zone_size}, Target: ≤{max_per_zone}")

    if max_zone_size > max_per_zone:
        logger.warning(f"Some zones exceed {max_per_zone} customers. Running final balancing...")
        zones = balance_dynamic_zones(zones, max_per_zone)

    end_time = time.time()
    computation_time = (end_time - start_time) * 1000

    logger.info(f"Clustering completed in {computation_time:.2f}ms for {len(customers)} customers into {len(zones)} zones")

    return zones, zone_centers, computation_time

def balance_zones_by_distance(zones: List[List[Dict]], max_per_zone: int, zone_centers: np.ndarray) -> List[List[Dict]]:
    """
    Improved zone balancing that considers distance when moving customers.
    """
    # Remove empty zones
    zones = [zone for zone in zones if zone]

    # Redistribute customers from oversized zones
    max_iterations = 50
    iteration = 0

    while iteration < max_iterations:
        moved_customer = False
        iteration += 1

        for zone_idx, zone in enumerate(zones):
            if len(zone) > max_per_zone:
                # Find the customer in this zone who is closest to another zone
                best_customer_to_move = None
                best_target_zone = None
                best_distance_improvement = 0

                for customer in zone:
                    customer_coord = np.array(customer["coords"])
                    current_distance = np.linalg.norm(customer_coord - zone_centers[zone_idx])

                    # Check all other zones that have space
                    for target_zone_idx, target_zone in enumerate(zones):
                        if target_zone_idx != zone_idx and len(target_zone) < max_per_zone:
                            target_distance = np.linalg.norm(customer_coord - zone_centers[target_zone_idx])
                            distance_improvement = current_distance - target_distance

                            # If this customer would be closer to the target zone
                            if distance_improvement > best_distance_improvement:
                                best_distance_improvement = distance_improvement
                                best_customer_to_move = customer
                                best_target_zone = target_zone_idx

                # Move the best customer if found
                if best_customer_to_move and best_target_zone is not None:
                    zone.remove(best_customer_to_move)
                    best_customer_to_move["zone_id"] = best_target_zone
                    zones[best_target_zone].append(best_customer_to_move)
                    moved_customer = True
                    logger.info(f"Moved customer {best_customer_to_move['id']} from zone {zone_idx} to zone {best_target_zone}")
                    break

        if not moved_customer:
            break

    return zones

def balance_dynamic_zones(zones: List[List[Dict]], max_per_zone: int) -> List[List[Dict]]:
    """Legacy balance function - kept for compatibility"""
    # Remove empty zones
    zones = [zone for zone in zones if zone]

    # Redistribute customers from oversized zones
    balanced = True
    while balanced:
        balanced = False

        for zone in zones:
            if len(zone) > max_per_zone:
                # Find zone with minimum customers
                min_zone_idx = min(range(len(zones)), key=lambda x: len(zones[x]))

                # Move customer to zone with fewer customers
                if len(zones[min_zone_idx]) < max_per_zone:
                    customer_to_move = zone.pop()
                    customer_to_move["zone_id"] = min_zone_idx
                    zones[min_zone_idx].append(customer_to_move)
                    balanced = True
                    break

    return zones

def perform_dynamic_zoning(filepath: str = "san_antonio_coordinates.csv") -> Dict:
    """
    Main function for dynamic zoning - computes zones on-the-fly without storing in database.
    This function is called every time a new customer is added or map refresh is needed.
    """
    import time
    start_time = time.time()

    try:
        # Load customer data (simulating database fetch)
        customers = load_and_process_customers(filepath)

        if not customers:
            return {
                "total_customers": 0,
                "routable_customers": 0,
                "total_zones": 0,
                "max_customers_per_zone": MAX_CUSTOMERS_PER_ZONE,
                "zones": [],
                "computation_time_ms": 0.0
            }

        # For this focused API, assume all customers are routable
        routable_customers = customers

        if not routable_customers:
            logger.warning("No routable customers found")
            return {
                "total_customers": len(customers),
                "routable_customers": 0,
                "total_zones": 0,
                "max_customers_per_zone": MAX_CUSTOMERS_PER_ZONE,
                "zones": [],
                "computation_time_ms": 0.0
            }

        # Perform dynamic clustering on routable customers
        zones, cluster_centers, _ = dynamic_cluster_customers(routable_customers, MAX_CUSTOMERS_PER_ZONE)

        # Format response
        formatted_zones = []
        for zone_id, zone_customers in enumerate(zones):
            if zone_customers:  # Only include non-empty zones
                # Calculate zone center
                if zone_id < len(cluster_centers):
                    center_lon, center_lat = cluster_centers[zone_id]
                else:
                    # Fallback: calculate center from customers
                    lats = [c["coords"][1] for c in zone_customers]
                    lons = [c["coords"][0] for c in zone_customers]
                    center_lat = sum(lats) / len(lats)
                    center_lon = sum(lons) / len(lons)

                # Format customers for this zone
                zone_customer_list = []
                for customer in zone_customers:
                    zone_customer_list.append({
                        "id": customer["id"],
                        "latitude": customer["coords"][1],
                        "longitude": customer["coords"][0],
                        "address": customer["address"]
                    })

                formatted_zones.append({
                    "zone_id": zone_id + 1,  # 1-based indexing
                    "customers": zone_customer_list,
                    "customer_count": len(zone_customers),
                    "center_latitude": center_lat,
                    "center_longitude": center_lon
                })

        end_time = time.time()
        total_time = (end_time - start_time) * 1000  # Convert to milliseconds

        logger.info(f"Dynamic zoning completed: {len(customers)} total customers, {len(routable_customers)} routable -> {len(formatted_zones)} zones in {total_time:.2f}ms")

        # Prepare result data
        result_data = {
            "total_customers": len(customers),
            "routable_customers": len(routable_customers),
            "total_zones": len(formatted_zones),
            "max_customers_per_zone": MAX_CUSTOMERS_PER_ZONE,
            "zones": formatted_zones,
            "computation_time_ms": round(total_time, 2)
        }

        # Store zones in JSON file
        try:
            import json
            with open("zones_output.json", "w") as f:
                json.dump(result_data, f, indent=2, default=str)
            logger.info(f"Zones saved to zones_output.json")
        except Exception as e:
            logger.warning(f"Failed to save zones to JSON: {e}")

        return result_data

    except Exception as e:
        logger.error(f"Error in dynamic zoning: {e}")
        raise

# FastAPI Endpoints
@app.get("/", response_class=HTMLResponse)
async def root():
    """Root endpoint with API documentation"""
    return """
    <html>
        <head>
            <title>Zoning API</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
                .container { background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
                h1 { color: #2c3e50; margin-bottom: 10px; }
                h2 { color: #34495e; border-bottom: 2px solid #3498db; padding-bottom: 5px; margin-top: 30px; }
                .subtitle { color: #7f8c8d; margin-bottom: 30px; font-size: 16px; }
                ul { margin: 15px 0; }
                li { margin: 8px 0; }
                .endpoint { background: #ecf0f1; padding: 3px 6px; border-radius: 3px; font-family: monospace; }
                .status { display: inline-block; padding: 2px 8px; border-radius: 12px; font-size: 12px; margin-left: 10px; }
                .active { background: #2ecc71; color: white; }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>🗺️ Zoning API</h1>
                <p class="subtitle">API for creating service zones using dynamic clustering</p>

                <h2>🎯 Available Endpoints</h2>
                <ul>
                    <li><span class="endpoint">GET /</span> - API documentation homepage <span class="status active">ACTIVE</span></li>
                    <li><span class="endpoint">GET /health</span> - Health check <span class="status active">ACTIVE</span></li>
                    <li><span class="endpoint">GET /api/zoning</span> - Generate dynamic zones <span class="status active">ACTIVE</span></li>
                    <li><span class="endpoint">GET /docs</span> - Swagger UI documentation <span class="status active">ACTIVE</span></li>
                </ul>

                <h2>🎯 Key Features</h2>
                <ul>
                    <li>✅ <strong>Dynamic zone creation</strong> - Real-time computation of service zones</li>
                    <li>✅ <strong>Optimal clustering</strong> - Maximum 11 customers per zone</li>
                    <li>✅ <strong>Fast computation</strong> - Zones calculated in milliseconds</li>
                    <li>✅ <strong>JSON output</strong> - Results saved to zones_output.json</li>
                </ul>

                <h2>🔗 Quick Links</h2>
                <ul>
                    <li><a href="/docs" target="_blank">📖 API Documentation (Swagger UI)</a></li>
                    <li><a href="/health" target="_blank">💚 Health Check</a></li>
                    <li><a href="/api/zoning" target="_blank">🗺️ Generate Zones</a></li>
                </ul>
            </div>
        </body>
    </html>
    """

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "Zoning API", "version": "1.0.0"}

@app.get("/api/zoning", response_model=DynamicZoningResponse)
async def get_dynamic_zoning():
    """
    Dynamic zoning endpoint - computes zones on-the-fly.
    Returns zones with maximum 11 customers each.
    """
    try:
        result = perform_dynamic_zoning()
        return DynamicZoningResponse(**result)

    except Exception as e:
        logger.error(f"Error in dynamic zoning API: {e}")
        raise HTTPException(status_code=500, detail=str(e))

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
